import { Injectable, inject, effect } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { User } from '../models/user.model';
import { UserDataService } from '../core/user-data/user-data.service';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private userDataService = inject(UserDataService);

  private userRoleSubject = new BehaviorSubject<string>('');
  userRole$ = this.userRoleSubject.asObservable();

  // Legacy support - convert UserProfile to User format
  private currentUserSubject = new BehaviorSubject<User>(this.getDefaultUser());
  currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    // Use effect to watch UserDataService and convert to legacy format
    effect(() => {
      const profile = this.userDataService.userProfile();
      if (profile) {
        const legacyUser: User = this.convertProfileToUser(profile);
        this.currentUserSubject.next(legacyUser);
        this.userRoleSubject.next(profile.role);
      } else {
        this.currentUserSubject.next(this.getDefaultUser());
        this.userRoleSubject.next('');
      }
    });
  }

  private getDefaultUser(): User {
    return {
      fullName: '',
      birthday: '',
      email: '',
      phoneNumber: '+216 00 000 000',
      governorate: 'Tunisie',
      gender: '',
      address: '',
      lastShoppingDate: 'Yesterday',
      role: '',
      avatar: 'assets/images/default-avatar.svg',
      showGenderPlaceholder: true,
      showAddressPlaceholder: true,
      showOccupationPlaceholder: true,
      showFullNamePlaceholder: true,
      hasCustomAvatar: false,
      avatarPositionY: 0
    };
  }

  private convertProfileToUser(profile: any): User {
    return {
      fullName: profile.fullName || '',
      birthday: profile.birthday || '',
      email: profile.email || '',
      phoneNumber: profile.phoneNumber || '+216 00 000 000',
      governorate: profile.governorate || 'Tunisie',
      gender: profile.gender || '',
      address: profile.address || '',
      lastShoppingDate: profile.lastShoppingDate || 'Yesterday',
      role: profile.role || '',
      avatar: profile.avatar || 'assets/images/default-avatar.svg',
      occupationType: profile.occupationType || undefined,
      occupationPlace: profile.occupationPlace || '',
      showGenderPlaceholder: profile.showGenderPlaceholder ?? true,
      showAddressPlaceholder: profile.showAddressPlaceholder ?? true,
      showOccupationPlaceholder: profile.showOccupationPlaceholder ?? true,
      showFullNamePlaceholder: profile.showFullNamePlaceholder ?? true,
      hasCustomAvatar: profile.hasCustomAvatar || false,
      avatarPositionY: profile.avatarPositionY || 0
    };
  }

  setUserRole(role: string) {
    this.userRoleSubject.next(role);
  }

  getUserRole(): string {
    return this.userRoleSubject.value;
  }

  getCurrentUser(): User {
    return this.currentUserSubject.value;
  }

  // Delegate to new UserDataService
  updateUser(user: User): Observable<User> {
    // Convert User to UserProfile format and update
    const profileUpdates = {
      fullName: user.fullName,
      birthday: user.birthday,
      email: user.email,
      phoneNumber: user.phoneNumber,
      governorate: user.governorate,
      gender: user.gender,
      address: user.address,
      avatar: user.avatar,
      hasCustomAvatar: user.hasCustomAvatar,
      avatarPositionY: user.avatarPositionY,
      role: user.role,
      occupationType: (user.occupationType as any) || '',
      occupationPlace: user.occupationPlace || '',
      showGenderPlaceholder: user.showGenderPlaceholder,
      showAddressPlaceholder: user.showAddressPlaceholder,
      showOccupationPlaceholder: user.showOccupationPlaceholder,
      showFullNamePlaceholder: user.showFullNamePlaceholder,
      lastShoppingDate: user.lastShoppingDate
    };

    return this.userDataService.updateProfile(profileUpdates).pipe(
      map(profile => this.convertProfileToUser(profile))
    );
  }

  updateUserField(field: keyof User, value: any): Observable<User> {
    // Map User field to UserProfile field and update
    return this.userDataService.updateField(field as any, value).pipe(
      map(profile => this.convertProfileToUser(profile))
    );
  }

  /**
   * Handle user login and update user email
   * @param emailOrUsername The email or username entered during login
   * @returns Observable of the updated user
   */
  handleLogin(emailOrUsername: string): Observable<User> {
    // Only update if it's an email (contains @)
    if (emailOrUsername.includes('@')) {
      const currentUser = this.getCurrentUser();
      const updatedUser = { ...currentUser, email: emailOrUsername };

      // In development mode, we'll still update the BehaviorSubject but won't persist to localStorage
      this.currentUserSubject.next(updatedUser);
      console.log('User logged in with email:', emailOrUsername);

      return of(updatedUser);
    }
    return of(this.getCurrentUser());
  }

  /**
   * Clear user data on logout
   */
  logout(): void {
    // In development mode, we don't need to remove from localStorage
    // localStorage.removeItem('currentUser');

    // Reset to default user
    this.currentUserSubject.next(this.getDefaultUser());
    console.log('User logged out');
  }
}