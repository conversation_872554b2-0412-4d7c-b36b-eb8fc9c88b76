import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ementRef, ViewChild, inject, effect, Injector, runInInjectionContext } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';

import { User } from '../models/user.model';
import { AuthService } from '../core/auth/auth.service';
import { UserDataService } from '../core/user-data/user-data.service';

@Component({
  selector: 'app-user-profile',
  standalone: true,
  imports: [CommonModule, FormsModule, TopNavbarComponent, SidebarComponent],
  templateUrl: './user-profile.component.html',
  styleUrl: './user-profile.component.scss'
})
export class UserProfileComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('previewImage') previewImage!: ElementRef;

  user: User = {
    fullName: '',
    birthday: '',
    email: '',
    phoneNumber: '+216 00 000 000',
    governorate: 'Tunisie',
    gender: '',
    address: '',
    lastShoppingDate: 'Yesterday',
    role: '',
    avatar: 'assets/images/default-avatar.svg',
    showGenderPlaceholder: true,
    showAddressPlaceholder: true,
    showOccupationPlaceholder: true,
    showFullNamePlaceholder: true,
    hasCustomAvatar: false,
    avatarPositionY: 0
  };
  editingField: string | null = null;
  tempFieldValue: any = null;
  editingAllFields = false;
  tempUser: User | null = null;

  // Inject services
  private authService = inject(AuthService);
  private userDataService = inject(UserDataService);
  private injector = inject(Injector);

  // Access auth signals
  protected readonly userFullName = this.authService.userFullName;
  protected readonly userEmail = this.authService.userEmail;
  protected readonly userAvatar = this.authService.userAvatar;

  // Access user data signals
  protected readonly userProfile = this.userDataService.userProfile;

  // Image repositioning properties
  showImageRepositionModal = false;
  tempImageSrc: string | null = null;
  tempImageFile: File | null = null;
  tempImagePosition = 0;
  isDragging = false;
  dragStartY = 0;
  dragStartPosition = 0;
  animationFrameId: number | null = null;

  constructor() {
    // Use effect to watch for user profile changes with complete data isolation
    runInInjectionContext(this.injector, () => {
      effect(() => {
        const profile = this.userDataService.userProfile();
        console.log('Effect triggered - profile:', profile);

        if (profile) {
          // Convert UserProfile to legacy User format for compatibility
          this.user = {
            fullName: profile.fullName,
            birthday: profile.birthday,
            email: profile.email,
            phoneNumber: profile.phoneNumber,
            governorate: profile.governorate,
            gender: profile.gender,
            address: profile.address,
            avatar: profile.avatar,
            hasCustomAvatar: profile.hasCustomAvatar,
            avatarPositionY: profile.avatarPositionY,
            role: profile.role,
            occupationType: profile.occupationType as any,
            occupationPlace: profile.occupationPlace,
            showGenderPlaceholder: profile.showGenderPlaceholder,
            showAddressPlaceholder: profile.showAddressPlaceholder,
            showOccupationPlaceholder: profile.showOccupationPlaceholder,
            showFullNamePlaceholder: profile.showFullNamePlaceholder,
            lastShoppingDate: profile.lastShoppingDate
          };

          console.log('=== USER PROFILE COMPONENT DATA ===');
          console.log('Profile from UserDataService:', profile);
          console.log('Converted user object:', this.user);
          console.log('Birthday:', this.user.birthday);
          console.log('Phone Number:', this.user.phoneNumber);
          console.log('Gender:', this.user.gender);
          console.log('Avatar:', this.user.avatar);
          console.log('=== END USER PROFILE DATA ===');
        } else {
          console.log('No profile data available, using default user');
        }
      });
    });
  }

  ngOnInit(): void {
    console.log('UserProfileComponent ngOnInit - checking auth user and profile data');

    // Check current auth user
    const currentAuthUser = this.authService.currentUser();
    console.log('Current auth user:', currentAuthUser);

    // Check current profile
    const currentProfile = this.userDataService.userProfile();
    console.log('Current user profile:', currentProfile);

    // Data clearing method available: this.clearAllUserData() - call manually if needed

    // Check localStorage for user profile data
    const storedProfile = localStorage.getItem('userProfile');
    if (storedProfile) {
      const profileData = JSON.parse(storedProfile);
      console.log('Found stored profile data:', profileData);
      
      // Update the user object with stored data
      this.user = {
        ...this.user,
        ...profileData
      };

      // Update AuthService to ensure top-navbar and sidebar avatars are in sync
      this.authService.updateUser({
        avatar: profileData.avatar,
        hasCustomAvatar: profileData.hasCustomAvatar,
        avatarPositionY: profileData.avatarPositionY
      } as any);
    }

    // Use effect to watch for user profile changes with complete data isolation
    runInInjectionContext(this.injector, () => {
      effect(() => {
        const profile = this.userDataService.userProfile();
        console.log('Effect triggered - profile:', profile);

        if (profile) {
          // Convert UserProfile to legacy User format for compatibility
          this.user = {
            fullName: profile.fullName,
            birthday: profile.birthday,
            email: profile.email,
            phoneNumber: profile.phoneNumber,
            governorate: profile.governorate,
            gender: profile.gender,
            address: profile.address,
            avatar: profile.avatar,
            hasCustomAvatar: profile.hasCustomAvatar,
            avatarPositionY: profile.avatarPositionY,
            role: profile.role,
            occupationType: profile.occupationType as any,
            occupationPlace: profile.occupationPlace,
            showGenderPlaceholder: profile.showGenderPlaceholder,
            showAddressPlaceholder: profile.showAddressPlaceholder,
            showOccupationPlaceholder: profile.showOccupationPlaceholder,
            showFullNamePlaceholder: profile.showFullNamePlaceholder,
            lastShoppingDate: profile.lastShoppingDate
          };

          // Store updated profile in localStorage
          localStorage.setItem('userProfile', JSON.stringify({
            fullName: profile.fullName,
            email: profile.email,
            birthday: profile.birthday,
            gender: profile.gender,
            phoneNumber: profile.phoneNumber,
            avatar: profile.avatar,
            hasCustomAvatar: profile.hasCustomAvatar,
            avatarPositionY: profile.avatarPositionY,
            role: profile.role,
            occupationType: profile.occupationType,
            occupationPlace: profile.occupationPlace
          }));

          console.log('=== USER PROFILE COMPONENT DATA ===');
          console.log('Profile from UserDataService:', profile);
          console.log('Converted user object:', this.user);
          console.log('Birthday:', this.user.birthday);
          console.log('Phone Number:', this.user.phoneNumber);
          console.log('Gender:', this.user.gender);
          console.log('Avatar:', this.user.avatar);
          console.log('=== END USER PROFILE DATA ===');
        }
      });
    });
  }

  startEditing(field: keyof User): void {
    this.editingField = field;

    if (field === 'role') {
      // Initialize occupation type and place
      this.occupationType = this.user.occupationType || null;
      this.occupationPlace = this.user.occupationPlace || null;
      this.tempFieldValue = this.user[field];
    } else if (field === 'phoneNumber') {
      // Initialize phone number without prefix
      const phoneValue = this.user[field] || '';
      if (phoneValue && phoneValue.startsWith(this.phonePrefix)) {
        // If the phone number already has the prefix, remove it for editing
        this.phoneNumberValue = phoneValue.substring(this.phonePrefix.length);
        this.tempFieldValue = this.phonePrefix + this.phoneNumberValue;
      } else if (phoneValue) {
        // If there's a phone number but no prefix
        this.phoneNumberValue = phoneValue;
        this.tempFieldValue = this.phonePrefix + phoneValue;
      } else {
        // Default placeholder
        this.phoneNumberValue = '00 000 000';
        this.tempFieldValue = this.phonePrefix + '00 000 000';
      }
    } else if (field === 'fullName') {
      // For full name, get from AuthService if available
      const authUser = this.authService.currentUser();
      this.tempFieldValue = authUser?.full_name || this.user[field] || '';
    } else if (field === 'email') {
      // For email, get from AuthService if available
      const authUser = this.authService.currentUser();
      this.tempFieldValue = authUser?.email || this.user[field] || '';
    } else if (field === 'birthday') {
      // For birthday, get from AuthService if available
      const authUser = this.authService.currentUser();
      this.tempFieldValue = (authUser as any)?.birthday || this.user[field] || '';
    } else if (field === 'gender') {
      // For gender, get from AuthService if available
      const authUser = this.authService.currentUser();
      this.tempFieldValue = (authUser as any)?.gender || this.user[field] || '';
    } else {
      this.tempFieldValue = this.user[field];
    }

    console.log(`Starting edit for ${field}:`, this.tempFieldValue);
  }

  // Phone number focus/blur handlers
  onPhoneFocus(): void {
    this.isPhoneFocused = true;
    if (this.phoneNumberValue === '00 000 000') {
      this.phoneNumberValue = '';
      this.tempFieldValue = '';
    }
  }

  onPhoneBlur(): void {
    this.isPhoneFocused = false;
    if (!this.phoneNumberValue) {
      this.phoneNumberValue = '00 000 000';
    }
  }

  // Handle phone number input
  onPhoneInput(event: any): void {
    // Filter out any non-numeric characters except spaces
    const input = event.target as HTMLInputElement;
    const cursorPosition = input.selectionStart || 0;
    const originalValue = this.phoneNumberValue;

    // Check if the input contains any non-numeric characters (except spaces)
    const hasNonNumeric = /[^\d\s]/.test(originalValue);

    // Replace any non-numeric and non-space characters
    const filteredValue = originalValue.replace(/[^0-9 ]/g, '');

    // Only update if the value has changed (something was filtered out)
    if (filteredValue !== originalValue) {
      this.phoneNumberValue = filteredValue;

      // Show error message if letters were entered
      if (hasNonNumeric) {
        this.phoneNumberError = "Only numbers are allowed";

        // Hide error after 3 seconds
        setTimeout(() => {
          if (this.phoneNumberError === "Only numbers are allowed") {
            this.phoneNumberError = null;
          }
        }, 3000);
      }

      // Restore cursor position after Angular updates the DOM
      setTimeout(() => {
        // Adjust cursor position if characters were removed before the cursor
        const newPosition = Math.max(0, cursorPosition - (originalValue.length - filteredValue.length));
        input.setSelectionRange(newPosition, newPosition);
      });
    }

    // Validate phone number length (excluding spaces)
    const digitsOnly = filteredValue.replace(/\s/g, '');
    if (digitsOnly.length > 0 && digitsOnly.length < 8) {
      this.phoneNumberError = "Invalid number (must be at least 8 digits)";
    } else if (digitsOnly.length >= 8) {
      this.phoneNumberError = null;
    }

    // Update the full phone number in the tempFieldValue
    this.tempFieldValue = this.phonePrefix + this.phoneNumberValue;
  }

  cancelEditing(): void {
    this.editingField = null;
    this.tempFieldValue = null;
    this.occupationType = null;
    this.occupationPlace = null;
    this.phoneNumberError = null;
  }

  // Helper function to capitalize first letter of each word
  capitalizeFirstLetter(text: string): string {
    if (!text) return text;
    return text.split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // For occupation type and place
  occupationType: 'Working' | 'Study' | 'Train' | 'Unemployed' | null = null;
  occupationPlace: string | null = null;

  // For phone number with country code
  phonePrefix = '+216 ';
  phoneNumberValue = '00 000 000';
  isPhoneFocused = false;
  phoneNumberError: string | null = null;

  saveField(field: keyof User): void {
    console.log(`Saving field ${field} with value:`, this.tempFieldValue);

    if (this.tempFieldValue !== null) {
      // Handle special cases for placeholders
      if (field === 'fullName' && this.tempFieldValue) {
        // Capitalize first letter of each word in full name
        const capitalizedName = this.capitalizeFirstLetter(this.tempFieldValue);

        // Update using new UserDataService for complete isolation
        this.userDataService.updateField('fullName', capitalizedName).subscribe({
          next: () => {
            this.userDataService.updateField('showFullNamePlaceholder', false);
            console.log('UserDataService updated fullName successfully');
          },
          error: (err) => console.error('UserDataService update failed:', err)
        });
      } else if (field === 'gender' && this.tempFieldValue) {
        // Update using new UserDataService for complete isolation
        this.userDataService.updateField('gender', this.tempFieldValue).subscribe({
          next: () => {
            this.userDataService.updateField('showGenderPlaceholder', false);
            console.log('UserDataService updated gender successfully');
          },
          error: (err) => console.error('UserDataService update failed:', err)
        });
      } else if (field === 'address' && this.tempFieldValue) {
        // Update using new UserDataService for complete isolation
        this.userDataService.updateField('address', this.tempFieldValue).subscribe({
          next: () => {
            this.userDataService.updateField('showAddressPlaceholder', false);
            console.log('UserDataService updated address successfully');
          },
          error: (err) => console.error('UserDataService update failed:', err)
        });
      } else if (field === 'phoneNumber') {
        // For phone number, ensure it has the country code prefix
        let phoneValue = this.tempFieldValue;

        // If it's the default placeholder or empty, don't save
        if (phoneValue === this.phonePrefix + '00 000 000' || phoneValue === this.phonePrefix) {
          this.editingField = null;
          this.tempFieldValue = null;
          this.phoneNumberError = null;
          return;
        }

        // Validate phone number length
        const digitsOnly = phoneValue.replace(/[^\d]/g, '');
        if (digitsOnly.length < 8) {
          this.phoneNumberError = "Invalid number (must be at least 8 digits)";
          return; // Don't save if invalid
        }

        // If it doesn't start with the prefix, add it
        if (!phoneValue.startsWith(this.phonePrefix)) {
          phoneValue = this.phonePrefix + phoneValue;
        }

        // Update using new UserDataService for complete isolation
        this.userDataService.updateField('phoneNumber', phoneValue).subscribe({
          next: () => console.log('UserDataService updated phoneNumber successfully'),
          error: (err) => console.error('UserDataService update failed:', err)
        });
        this.phoneNumberError = null;
      } else if (field === 'role') {
        // For occupation, we now have type and place
        if (this.occupationType) {
          let updatedUser: User;

          if (this.occupationType === 'Unemployed') {
            // If unemployed, we don't need place
            updatedUser = {
              ...this.user,
              role: this.occupationType,
              occupationType: this.occupationType,
              occupationPlace: '',
              showOccupationPlaceholder: false
            };
          } else {
            // For other types, we need both type and place
            updatedUser = {
              ...this.user,
              role: this.occupationType + (this.occupationPlace ? ` at ${this.occupationPlace}` : ''),
              occupationType: this.occupationType,
              occupationPlace: this.occupationPlace || '',
              showOccupationPlaceholder: false
            };
          }

          // Update using new UserDataService for complete isolation
          this.userDataService.updateProfile({
            role: updatedUser.role,
            occupationType: updatedUser.occupationType as any,
            occupationPlace: updatedUser.occupationPlace,
            showOccupationPlaceholder: false
          }).subscribe({
            next: () => console.log('UserDataService updated occupation successfully'),
            error: (err) => console.error('UserDataService update failed:', err)
          });
        }
      } else if (field === 'birthday' && this.tempFieldValue) {
        // Update using new UserDataService for complete isolation
        this.userDataService.updateField('birthday', this.tempFieldValue).subscribe({
          next: () => console.log('UserDataService updated birthday successfully'),
          error: (err) => console.error('UserDataService update failed:', err)
        });
      } else if (field === 'email' && this.tempFieldValue) {
        // Update using new UserDataService for complete isolation
        this.userDataService.updateField('email', this.tempFieldValue).subscribe({
          next: () => console.log('UserDataService updated email successfully'),
          error: (err) => console.error('UserDataService update failed:', err)
        });
      } else {
        // For other fields, update using new UserDataService for complete isolation
        this.userDataService.updateField(field as any, this.tempFieldValue).subscribe({
          next: () => console.log(`UserDataService updated ${field} successfully`),
          error: (err) => console.error('UserDataService update failed:', err)
        });
      }

      this.editingField = null;
      this.tempFieldValue = null;
      this.occupationType = null;
      this.occupationPlace = null;

      this.isPhoneFocused = false;
    }
  }

  startEditingAll(): void {
    this.editingAllFields = true;
    this.tempUser = { ...this.user };

    // Initialize occupation type and place for edit all form
    this.tempOccupationType = this.user.occupationType || null;
    this.tempOccupationPlace = this.user.occupationPlace || null;

    // Initialize phone number value
    if (this.user.phoneNumber && this.user.phoneNumber.startsWith(this.phonePrefix)) {
      this.tempPhoneNumberValue = this.user.phoneNumber.substring(this.phonePrefix.length);
    } else if (this.user.phoneNumber) {
      this.tempPhoneNumberValue = this.user.phoneNumber;
    } else {
      this.tempPhoneNumberValue = '00 000 000';
    }
  }

  cancelEditingAll(): void {
    this.editingAllFields = false;
    this.tempUser = null;
    this.tempOccupationType = null;
    this.tempOccupationPlace = null;
    this.tempPhoneNumberValue = '';
    this.tempPhoneNumberError = null;
  }

  // For edit all form
  tempOccupationType: 'Working' | 'Study' | 'Train' | 'Unemployed' | null = null;
  tempOccupationPlace: string | null = null;
  tempPhoneNumberValue: string = '';
  tempPhoneNumberError: string | null = null;

  // Phone number handlers for edit all form
  onPhoneInputAll(event: any): void {
    // Filter out any non-numeric characters except spaces
    const input = event.target as HTMLInputElement;
    const cursorPosition = input.selectionStart || 0;
    const originalValue = this.tempPhoneNumberValue;

    // Check if the input contains any non-numeric characters (except spaces)
    const hasNonNumeric = /[^\d\s]/.test(originalValue);

    // Replace any non-numeric and non-space characters
    const filteredValue = originalValue.replace(/[^0-9 ]/g, '');

    // Only update if the value has changed (something was filtered out)
    if (filteredValue !== originalValue) {
      this.tempPhoneNumberValue = filteredValue;

      // Show error message if letters were entered
      if (hasNonNumeric) {
        this.tempPhoneNumberError = "Only numbers are allowed";

        // Hide error after 3 seconds
        setTimeout(() => {
          if (this.tempPhoneNumberError === "Only numbers are allowed") {
            this.tempPhoneNumberError = null;
          }
        }, 3000);
      }

      // Restore cursor position after Angular updates the DOM
      setTimeout(() => {
        // Adjust cursor position if characters were removed before the cursor
        const newPosition = Math.max(0, cursorPosition - (originalValue.length - filteredValue.length));
        input.setSelectionRange(newPosition, newPosition);
      });
    }

    // Validate phone number length (excluding spaces)
    const digitsOnly = filteredValue.replace(/\s/g, '');
    if (digitsOnly.length > 0 && digitsOnly.length < 8) {
      this.tempPhoneNumberError = "Invalid number (must be at least 8 digits)";
    } else if (digitsOnly.length >= 8) {
      this.tempPhoneNumberError = null;
    }

    // Update the full phone number in the user object
    if (this.tempUser) {
      this.tempUser.phoneNumber = this.phonePrefix + this.tempPhoneNumberValue;
    }
  }

  onPhoneFocusAll(): void {
    // If the value is the placeholder, clear it
    if (this.tempPhoneNumberValue === '00 000 000') {
      this.tempPhoneNumberValue = '';
    }
  }

  onPhoneBlurAll(): void {
    // If empty, restore the placeholder
    if (!this.tempPhoneNumberValue || this.tempPhoneNumberValue === '') {
      this.tempPhoneNumberValue = '';
      if (this.tempUser) {
        this.tempUser.phoneNumber = '';
      }
    } else {
      // Ensure the phone number has the prefix
      if (this.tempUser) {
        this.tempUser.phoneNumber = this.phonePrefix + this.tempPhoneNumberValue;
      }
    }
  }

  saveAllFields(): void {
    if (this.tempUser) {
      // Validate phone number length
      if (this.tempPhoneNumberValue && this.tempPhoneNumberValue !== '00 000 000') {
        const digitsOnly = this.tempPhoneNumberValue.replace(/[^\d]/g, '');
        if (digitsOnly.length < 8) {
          this.tempPhoneNumberError = "Invalid number (must be at least 8 digits)";
          return; // Don't save if phone number is invalid
        }
      }

      // Update placeholder flags based on filled fields
      if (this.tempUser.fullName) {
        // Capitalize first letter of each word in full name
        this.tempUser.fullName = this.capitalizeFirstLetter(this.tempUser.fullName);
        this.tempUser.showFullNamePlaceholder = false;
      }
      if (this.tempUser.gender) {
        this.tempUser.showGenderPlaceholder = false;
      }
      if (this.tempUser.address) {
        this.tempUser.showAddressPlaceholder = false;
      }

      // Handle occupation type and place
      if (this.tempOccupationType) {
        this.tempUser.occupationType = this.tempOccupationType;

        if (this.tempOccupationType === 'Unemployed') {
          this.tempUser.occupationPlace = '';
          this.tempUser.role = this.tempOccupationType;
        } else {
          this.tempUser.occupationPlace = this.tempOccupationPlace || '';
          this.tempUser.role = this.tempOccupationType +
            (this.tempOccupationPlace ? ` at ${this.tempOccupationPlace}` : '');
        }

        this.tempUser.showOccupationPlaceholder = false;
      }

      // Update using new UserDataService for complete isolation
      const profileUpdates = {
        fullName: this.tempUser.fullName,
        email: this.tempUser.email,
        birthday: this.tempUser.birthday,
        gender: this.tempUser.gender,
        phoneNumber: this.tempUser.phoneNumber,
        address: this.tempUser.address,
        governorate: this.tempUser.governorate,
        role: this.tempUser.role,
        occupationType: this.tempUser.occupationType as any,
        occupationPlace: this.tempUser.occupationPlace,
        showFullNamePlaceholder: this.tempUser.showFullNamePlaceholder,
        showGenderPlaceholder: this.tempUser.showGenderPlaceholder,
        showAddressPlaceholder: this.tempUser.showAddressPlaceholder,
        showOccupationPlaceholder: this.tempUser.showOccupationPlaceholder
      };

      this.userDataService.updateProfile(profileUpdates).subscribe({
        next: () => console.log('UserDataService updated all fields successfully'),
        error: (err) => console.error('UserDataService update failed:', err)
      });

      this.editingAllFields = false;
      this.tempUser = null;
      this.tempOccupationType = null;
      this.tempOccupationPlace = null;
      this.tempPhoneNumberValue = '';
      this.tempPhoneNumberError = null;
    }
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const img = new Image();
        img.onload = () => {
          // Create a canvas to resize the image
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const maxSize = 400; // Maximum size for the image
          
          // Calculate new dimensions while maintaining aspect ratio
          let width = img.width;
          let height = img.height;
          if (width > height) {
            if (width > maxSize) {
              height = Math.round((height * maxSize) / width);
              width = maxSize;
            }
          } else {
            if (height > maxSize) {
              width = Math.round((width * maxSize) / height);
              height = maxSize;
            }
          }
          
          canvas.width = width;
          canvas.height = height;
          
          // Draw and resize image
          ctx?.drawImage(img, 0, 0, width, height);
          
          // Convert to base64
          const base64Image = canvas.toDataURL('image/jpeg', 0.8);
          
          // Update user avatar
          this.user.avatar = base64Image;
          this.user.hasCustomAvatar = true;
          
          // Save to UserDataService
          this.userDataService.updateField('avatar', base64Image).subscribe({
            next: () => {
              this.userDataService.updateField('hasCustomAvatar', true).subscribe({
                next: () => {
                  // Update AuthService to sync top-navbar and sidebar avatars
                  this.authService.updateUser({
                    avatar: base64Image,
                    hasCustomAvatar: true
                  } as any);

                  // Update localStorage
                  const storedProfile = localStorage.getItem('userProfile');
                  if (storedProfile) {
                    const profileData = JSON.parse(storedProfile);
                    profileData.avatar = base64Image;
                    profileData.hasCustomAvatar = true;
                    localStorage.setItem('userProfile', JSON.stringify(profileData));
                  }

                  console.log('Avatar updated successfully');
                },
                error: (err) => console.error('Error updating avatar:', err)
              });
            },
            error: (err) => console.error('Error updating avatar:', err)
          });
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  removeAvatar(): void {
    this.user.avatar = 'assets/images/default-avatar.svg';
    this.user.hasCustomAvatar = false;
    this.user.avatarPositionY = 0;
    
    // Update in UserDataService
    this.userDataService.updateField('avatar', this.user.avatar).subscribe({
      next: () => {
        this.userDataService.updateField('hasCustomAvatar', false).subscribe({
          next: () => {
            this.userDataService.updateField('avatarPositionY', 0).subscribe({
              next: () => {
                // Update AuthService to sync top-navbar and sidebar avatars
                this.authService.updateUser({
                  avatar: this.user.avatar,
                  hasCustomAvatar: false,
                  avatarPositionY: 0
                } as any);

                // Update localStorage
                const storedProfile = localStorage.getItem('userProfile');
                if (storedProfile) {
                  const profileData = JSON.parse(storedProfile);
                  profileData.avatar = this.user.avatar;
                  profileData.hasCustomAvatar = false;
                  profileData.avatarPositionY = 0;
                  localStorage.setItem('userProfile', JSON.stringify(profileData));
                }

                console.log('Avatar removed successfully');
              },
              error: (err) => console.error('Error removing avatar:', err)
            });
          },
          error: (err) => console.error('Error removing avatar:', err)
        });
      },
      error: (err) => console.error('Error removing avatar:', err)
    });
  }

  startDragging(event: MouseEvent): void {
    this.isDragging = true;
    this.dragStartY = event.clientY;
    this.dragStartPosition = this.user.avatarPositionY || 0;
    
    // Add event listeners for drag and end
    document.addEventListener('mousemove', this.onDrag.bind(this));
    document.addEventListener('mouseup', this.stopDragging.bind(this));
    
    // Prevent default behavior
    event.preventDefault();
  }

  onDrag(event: MouseEvent): void {
    if (!this.isDragging) return;
    
    const deltaY = event.clientY - this.dragStartY;
    const newPosition = this.dragStartPosition + deltaY;
    
    // Limit the movement range (adjust these values as needed)
    const maxPosition = 100;
    const minPosition = -100;
    const boundedPosition = Math.max(minPosition, Math.min(maxPosition, newPosition));
    
    // Update the position
    this.user.avatarPositionY = boundedPosition;
    
    // Request animation frame for smooth movement
    this.animationFrameId = requestAnimationFrame(() => {
      if (this.previewImage) {
        this.previewImage.nativeElement.style.transform = `translateY(${boundedPosition}px)`;
      }
    });
  }

  stopDragging(): void {
    if (!this.isDragging) return;
    
    this.isDragging = false;
    
    // Remove event listeners
    document.removeEventListener('mousemove', this.onDrag.bind(this));
    document.removeEventListener('mouseup', this.stopDragging.bind(this));
    
    // Cancel any pending animation frame
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  cancelImageRepositioning(): void {
    this.showImageRepositionModal = false;
    this.tempImageSrc = null;
    this.tempImageFile = null;
    this.tempImagePosition = 0;
    this.stopDragging();
  }

  saveRepositionedImage(): void {
    if (this.tempImageFile) {
      // Update the user's avatar position
      this.user.avatarPositionY = this.tempImagePosition;
      
      // Save the new position to UserDataService
      this.userDataService.updateField('avatarPositionY', this.tempImagePosition).subscribe({
        next: () => {
          console.log('Avatar position updated successfully');
          this.showImageRepositionModal = false;
          this.tempImageSrc = null;
          this.tempImageFile = null;
          this.tempImagePosition = 0;
        },
        error: (err) => {
          console.error('Error updating avatar position:', err);
        }
      });
    }
  }

  openImagePositionModal(): void {
    // Allow positioning for any avatar (custom or default)
    if (this.user.avatar) {
      this.tempImageSrc = this.user.avatar;
      this.tempImagePosition = this.user.avatarPositionY || 0;
      this.showImageRepositionModal = true;
    } else {
      // If no avatar, use default
      this.tempImageSrc = 'assets/images/default-avatar.svg';
      this.tempImagePosition = 0;
      this.showImageRepositionModal = true;
    }
  }

  /**
   * Clear all user data from localStorage and reset services
   * HOW I CLEAR DATA:
   * 1. Clear all localStorage data
   * 2. Reset AuthService (logout)
   * 3. Reset UserDataService
   * 4. Reset component state
   */
  clearAllUserData(): void {
    console.log('=== CLEARING ALL USER DATA ===');

    // 1. Clear all localStorage
    localStorage.clear();
    console.log('✅ localStorage cleared');

    // 2. Reset AuthService (this will trigger logout)
    this.authService.logout();
    console.log('✅ AuthService reset (user logged out)');

    // 3. Reset UserDataService
    this.userDataService.clearUserData();
    console.log('✅ UserDataService reset');

    // 4. Reset component state
    this.user = {
      fullName: '',
      birthday: '',
      email: '',
      phoneNumber: '+216 00 000 000',
      governorate: 'Tunisie',
      gender: '',
      address: '',
      lastShoppingDate: 'Yesterday',
      role: '',
      avatar: 'assets/images/default-avatar.svg',
      showGenderPlaceholder: true,
      showAddressPlaceholder: true,
      showOccupationPlaceholder: true,
      showFullNamePlaceholder: true,
      hasCustomAvatar: false,
      avatarPositionY: 0
    };
    console.log('✅ Component state reset');

    console.log('=== ALL USER DATA CLEARED ===');
    console.log('You will be redirected to login page...');

    // Redirect to login after clearing data
    setTimeout(() => {
      window.location.href = '/login';
    }, 2000);
  }

  ngOnDestroy(): void {
    // Clean up all resources
    this.stopDragging();

    // Cancel any pending animation frames
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }
}
